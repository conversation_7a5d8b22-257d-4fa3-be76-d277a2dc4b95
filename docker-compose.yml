# Optimized Docker Compose for RapidTrader
# Ultra-fast, minimal resource usage, production-ready

version: '3.8'

x-rapidtrader-optimized: &rapidtrader-optimized
  build:
    context: .
    dockerfile: Dockerfile.alpine
    args:
      - BUILDKIT_INLINE_CACHE=1
  image: rapidtrader:optimized-alpine
  restart: unless-stopped
  volumes:
    - rapidtrader-userdata:/rapidtrader/userdata
    - rapidtrader-logs:/rapidtrader/logs
    - rapidtrader-cache:/rapidtrader/cache
  env_file:
    - .env
  environment:
    - PYTHONOPTIMIZE=2
    - PYTHONUNBUFFERED=1
    - MALLOC_TRIM_THRESHOLD_=100000
    - RAPIDTRADER_CACHE_SIZE=1000
    - RAPIDTRADER_BATCH_SIZE=10
  networks:
    - rapidtrader-optimized
  deploy:
    resources:
      limits:
        memory: 512M
        cpus: '1.0'
      reservations:
        memory: 256M
        cpus: '0.5'
  security_opt:
    - no-new-privileges:true
  read_only: true
  tmpfs:
    - /tmp:noexec,nosuid,size=100m
    - /var/tmp:noexec,nosuid,size=50m

services:
  # High-performance backtesting
  backtest-optimized:
    <<: *rapidtrader-optimized
    container_name: rapidtrader-backtest-optimized
    command: backtest --optimized
    profiles:
      - backtest-optimized
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/backtest-config.json
      - RAPIDTRADER_MODE=backtest
      - RAPIDTRADER_OPTIMIZATION=true
    restart: "no"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '2.0'

  # Ultra-fast dry run trading
  dryrun-optimized:
    <<: *rapidtrader-optimized
    container_name: rapidtrader-dryrun-optimized
    command: dryrun --optimized --batch-orders
    profiles:
      - dryrun-optimized
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/dryrun-config.json
      - RAPIDTRADER_MODE=dryrun
      - RAPIDTRADER_BATCH_ORDERS=true
      - RAPIDTRADER_ASYNC_DATA=true

  # High-performance live trading
  live-optimized:
    <<: *rapidtrader-optimized
    container_name: rapidtrader-live-optimized
    command: live --optimized --fast-execution
    profiles:
      - live-optimized
    environment:
      - CONFIG_FILE=/rapidtrader/userdata/config/live-config.json
      - RAPIDTRADER_MODE=live
      - RAPIDTRADER_FAST_EXECUTION=true
      - RAPIDTRADER_CONNECTION_POOL=true

  # Optimized API Gateway
  api-gateway-optimized:
    build:
      context: .
      dockerfile: api_gateway/Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
    image: rapidtrader-api:optimized
    container_name: rapidtrader-api-optimized
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - rapidtrader-logs:/app/logs
    environment:
      - RAPIDTRADER_API_WORKERS=4
      - RAPIDTRADER_API_ASYNC=true
      - RAPIDTRADER_API_CACHE=true
    networks:
      - rapidtrader-optimized
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=50m
    profiles:
      - api-optimized

  # Optimized Frontend
  frontend-optimized:
    build:
      context: frontend_v2
      dockerfile: Dockerfile
      target: production
      args:
        - BUILDKIT_INLINE_CACHE=1
        - NODE_ENV=production
    image: rapidtrader-frontend:optimized
    container_name: rapidtrader-frontend-optimized
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://api-gateway-optimized:8000
    networks:
      - rapidtrader-optimized
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
    security_opt:
      - no-new-privileges:true
    read_only: true
    profiles:
      - frontend-optimized

  # Data cache service for ultra-fast data access
  data-cache:
    image: redis:7-alpine
    container_name: rapidtrader-data-cache
    restart: unless-stopped
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru --save ""
    volumes:
      - rapidtrader-redis-data:/data
    networks:
      - rapidtrader-optimized
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    security_opt:
      - no-new-privileges:true
    profiles:
      - cache-optimized

  # Optimized market data service
  market-data-optimized:
    <<: *rapidtrader-optimized
    container_name: rapidtrader-market-data-optimized
    command: python -m data.market_data_service --optimized --cache-redis
    environment:
      - RAPIDTRADER_REDIS_URL=redis://data-cache:6379
      - RAPIDTRADER_DATA_CACHE_SIZE=2000
      - RAPIDTRADER_ASYNC_FETCH=true
    depends_on:
      - data-cache
    profiles:
      - market-data-optimized

  # Performance monitoring
  monitoring:
    image: prom/prometheus:latest
    container_name: rapidtrader-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - rapidtrader-prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
    networks:
      - rapidtrader-optimized
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    profiles:
      - monitoring

  # Log aggregation
  log-aggregator:
    image: grafana/loki:latest
    container_name: rapidtrader-logs
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./logging/loki-config.yml:/etc/loki/local-config.yaml:ro
      - rapidtrader-loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - rapidtrader-optimized
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
    profiles:
      - logging

volumes:
  rapidtrader-userdata:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./userdata
  rapidtrader-logs:
    driver: local
  rapidtrader-cache:
    driver: local
  rapidtrader-redis-data:
    driver: local
  rapidtrader-prometheus-data:
    driver: local
  rapidtrader-loki-data:
    driver: local

networks:
  rapidtrader-optimized:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: rapidtrader-opt
    ipam:
      config:
        - subnet: **********/16
