#!/usr/bin/env python3
"""
Generate Fyers Access Token using Auth Code

This script uses the auth code you provided to generate fresh access and refresh tokens.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv, set_key

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

# Import the required module from the fyers_apiv3 package
from fyers_apiv3 import fyersModel

def generate_tokens():
    """Generate access token using the provided auth code."""

    # Get credentials from .env
    client_id = os.getenv('FYERS_CLIENT_ID')
    secret_key = os.getenv('FYERS_SECRET_KEY')
    redirect_uri = os.getenv('FYERS_REDIRECT_URI', 'https://127.0.0.1/')

    print("🔐 Fyers Token Generation")
    print("=" * 40)
    print(f"Client ID: {client_id}")
    print(f"Secret Key: {secret_key[:8]}..." if secret_key else "Not set")
    print(f"Redirect URI: {redirect_uri}")

    # Configuration
    response_type = "code"
    grant_type = "authorization_code"

    # The fresh authorization code from the redirect URL
    auth_code = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcHBfaWQiOiI3QTlOWDdOMk5XIiwidXVpZCI6IjExNmYzODQxMGE5MzQ3YzViNDA0MmY2ZDhjMzU4ZTlkIiwiaXBBZGRyIjoiIiwibm9uY2UiOiIiLCJzY29wZSI6IiIsImRpc3BsYXlfbmFtZSI6IkZBQTY4MDU0Iiwib21zIjoiSzEiLCJoc21fa2V5IjoiZTFlMjc5YzQ0YzM3ODM1NTRjMmMzZWNkMTY0N2Y3Njc5MDE3ZTM2MDA4OTgzYzUxOWU4MmRmMTQiLCJpc0RkcGlFbmFibGVkIjoiTiIsImlzTXRmRW5hYmxlZCI6Ik4iLCJhdWQiOiJbXCJkOjFcIixcImQ6MlwiLFwieDowXCIsXCJ4OjFcIixcIng6MlwiXSIsImV4cCI6MTc0ODY5NjM5OCwiaWF0IjoxNzQ4NjY2Mzk4LCJpc3MiOiJhcGkubG9naW4uZnllcnMuaW4iLCJuYmYiOjE3NDg2NjYzOTgsInN1YiI6ImF1dGhfY29kZSJ9.po_Ob7ro-BDzzsvj-FmMs3LBcj568DM5m3jhvtCrL-s"

    print(f"\n🔑 Using auth code: {auth_code[:50]}...")

    try:
        # Create a session object to handle the Fyers API authentication and token generation
        session = fyersModel.SessionModel(
            client_id=client_id,
            secret_key=secret_key,
            redirect_uri=redirect_uri,
            response_type=response_type,
            grant_type=grant_type
        )

        print("\n🔄 Setting auth code in session...")
        # Set the authorization code in the session object
        session.set_token(auth_code)

        print("🔄 Generating access token...")
        # Generate the access token using the authorization code
        response = session.generate_token()

        # Print the response
        print("\n📋 Token Generation Response:")
        print("=" * 50)
        print(response)

        if response.get('s') == 'ok':
            access_token = response.get('access_token')
            refresh_token = response.get('refresh_token', '')

            print("\n✅ Token generation successful!")
            print(f"Access Token: {access_token[:50]}...")
            print(f"Refresh Token: {refresh_token[:50]}..." if refresh_token else "No refresh token")

            # Update .env file
            env_file = project_root / '.env'
            print(f"\n💾 Updating .env file...")

            set_key(env_file, 'FYERS_AUTH_CODE', auth_code)
            set_key(env_file, 'FYERS_ACCESS_TOKEN', access_token)
            if refresh_token:
                set_key(env_file, 'FYERS_REFRESH_TOKEN', refresh_token)

            print("✅ .env file updated successfully!")

            # Test the connection
            print("\n🧪 Testing connection...")
            test_connection(client_id, access_token)

            return True

        else:
            print(f"\n❌ Token generation failed!")
            print(f"Error: {response}")
            return False

    except Exception as e:
        print(f"\n❌ Error during token generation: {e}")
        return False

def test_connection(client_id, access_token):
    """Test the Fyers connection with the new token."""
    try:
        # Create Fyers client
        fyers = fyersModel.FyersModel(
            client_id=client_id,
            token=access_token,
            log_path=""
        )

        # Test profile API
        profile = fyers.get_profile()

        if profile.get('s') == 'ok':
            user_data = profile.get('data', {})
            print("✅ Connection test successful!")
            print(f"   User ID: {user_data.get('fy_id', 'N/A')}")
            print(f"   Name: {user_data.get('name', 'N/A')}")
            print(f"   Email: {user_data.get('email_id', 'N/A')}")
            print(f"   Mobile: {user_data.get('mobile_number', 'N/A')}")

            # Test funds API
            funds = fyers.funds()
            if funds.get('s') == 'ok':
                print("✅ Funds API working!")
                fund_data = funds.get('fund_limit', [])
                if fund_data:
                    print(f"   Available Balance: ₹{fund_data[0].get('availablecash', 0):,.2f}")

            return True
        else:
            print(f"❌ Connection test failed: {profile}")
            return False

    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def main():
    """Main function."""
    print("🎯 Fyers Token Generator for RapidTrader")
    print("=" * 50)

    success = generate_tokens()

    if success:
        print("\n🎉 Fyers authentication completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart RapidTrader to use the new tokens:")
        print("   python start_rapidtrader.py demo")
        print("2. Check the frontend - Fyers should now show as connected")
        print("3. Test trading in dry-run mode")
        print("4. The frontend will show Fyers as connected with your account details")
    else:
        print("\n❌ Token generation failed")
        print("Please check the error messages above and try again")

if __name__ == "__main__":
    main()
