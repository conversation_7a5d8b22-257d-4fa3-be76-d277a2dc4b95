#!/usr/bin/env python3
"""
Fyers Authentication URL Generator

This script generates the Fyers authentication URL and opens it in your browser.
"""

import os
import webbrowser
from dotenv import load_dotenv

# Load environment
load_dotenv()

def generate_auth_url():
    """Generate Fyers authentication URL."""
    
    # Get credentials from .env
    client_id = os.getenv('FYERS_CLIENT_ID')
    redirect_uri = os.getenv('FYERS_REDIRECT_URI', 'https://127.0.0.1/')
    
    print("🔐 Fyers Authentication URL Generator")
    print("=" * 50)
    print(f"Client ID: {client_id}")
    print(f"Redirect URI: {redirect_uri}")
    
    if not client_id:
        print("❌ FYERS_CLIENT_ID not found in .env file")
        return None
    
    # Generate auth URL
    auth_url = (
        f"https://api-t2.fyers.in/vagator/v2/auth"
        f"?client_id={client_id}"
        f"&redirect_uri={redirect_uri}"
        f"&response_type=code"
        f"&state=sample_state"
    )
    
    print(f"\n🔗 Authentication URL:")
    print(f"{auth_url}")
    
    return auth_url

def main():
    """Main function."""
    print("🎯 Fyers Authentication - Step 1: Get Auth Code")
    print("=" * 60)
    
    auth_url = generate_auth_url()
    
    if auth_url:
        print(f"\n🚀 Opening authentication URL in browser...")
        try:
            webbrowser.open(auth_url)
            print("✅ Browser opened successfully!")
        except Exception as e:
            print(f"❌ Failed to open browser: {e}")
            print("Please manually copy and paste the URL above into your browser")
        
        print(f"\n📋 Instructions:")
        print("1. Complete the login process in your browser")
        print("2. After successful login, you'll be redirected to a URL like:")
        print("   https://127.0.0.1/?s=ok&code=200&auth_code=YOUR_AUTH_CODE&state=sample_state")
        print("3. Copy the 'auth_code' parameter from the URL")
        print("4. Run the token generation script with the new auth code")
        
        print(f"\n⏰ Note: Auth codes expire in 5-10 minutes, so complete the next step quickly!")
        
    else:
        print("❌ Failed to generate authentication URL")

if __name__ == "__main__":
    main()
