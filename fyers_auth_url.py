#!/usr/bin/env python3
"""
Fyers Authentication URL Generator

This script generates the Fyers authentication URL and opens it in your browser.
"""

import os
import webbrowser
from dotenv import load_dotenv

# Load environment
load_dotenv()

def generate_auth_url():
    """Generate Fyers authentication URL using the official API."""

    # Get credentials from .env
    client_id = os.getenv('FYERS_CLIENT_ID')
    secret_key = os.getenv('FYERS_SECRET_KEY')
    redirect_uri = os.getenv('FYERS_REDIRECT_URI', 'https://127.0.0.1/')

    print("🔐 Fyers Authentication URL Generator")
    print("=" * 50)
    print(f"Client ID: {client_id}")
    print(f"Secret Key: {secret_key[:8]}..." if secret_key else "Not set")
    print(f"Redirect URI: {redirect_uri}")

    if not client_id or not secret_key:
        print("❌ FYERS_CLIENT_ID or FYERS_SECRET_KEY not found in .env file")
        return None

    try:
        # Import Fyers API
        from fyers_apiv3 import fyersModel
        print("✅ Fyers API imported successfully")

        # Create session to generate auth URL
        session = fyersModel.SessionModel(
            client_id=client_id,
            secret_key=secret_key,
            redirect_uri=redirect_uri,
            response_type="code",
            grant_type="authorization_code"
        )

        # Generate auth URL
        auth_url = session.generate_authcode()

        print(f"\n🔗 Authentication URL:")
        print(f"{auth_url}")

        return auth_url

    except ImportError as e:
        print(f"❌ Failed to import Fyers API: {e}")
        print("Try: pip install fyers-apiv3")
        return None
    except Exception as e:
        print(f"❌ Error generating auth URL: {e}")
        return None

def main():
    """Main function."""
    print("🎯 Fyers Authentication - Step 1: Get Auth Code")
    print("=" * 60)

    auth_url = generate_auth_url()

    if auth_url:
        print(f"\n🚀 Opening authentication URL in browser...")
        try:
            webbrowser.open(auth_url)
            print("✅ Browser opened successfully!")
        except Exception as e:
            print(f"❌ Failed to open browser: {e}")
            print("Please manually copy and paste the URL above into your browser")

        print(f"\n📋 Instructions:")
        print("1. Complete the login process in your browser")
        print("2. After successful login, you'll be redirected to a URL like:")
        print("   https://127.0.0.1/?s=ok&code=200&auth_code=YOUR_AUTH_CODE&state=sample_state")
        print("3. Copy the 'auth_code' parameter from the URL")
        print("4. Run the token generation script with the new auth code")

        print(f"\n⏰ Note: Auth codes expire in 5-10 minutes, so complete the next step quickly!")

    else:
        print("❌ Failed to generate authentication URL")

if __name__ == "__main__":
    main()
