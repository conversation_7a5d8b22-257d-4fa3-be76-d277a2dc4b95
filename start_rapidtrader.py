#!/usr/bin/env python3
"""
RapidTrader Startup Script

This script provides an easy way to start RapidTrader components:
- API Gateway (FastAPI backend)
- Web UI (Flask frontend)
- Combined mode (both together)

Usage:
    python start_rapidtrader.py api          # Start API Gateway only
    python start_rapidtrader.py ui           # Start Web UI only
    python start_rapidtrader.py full         # Start both API Gateway and Web UI
    python start_rapidtrader.py demo         # Start demo with API key creation
"""

import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def start_api_gateway():
    """Start the API Gateway"""
    print("🚀 Starting RapidTrader API Gateway...")
    
    # Change to project directory
    os.chdir(project_root)
    
    # Start API Gateway
    cmd = [sys.executable, "-m", "api_gateway.main", "--host", "0.0.0.0", "--port", "8000"]
    return subprocess.Popen(cmd)

def start_web_ui():
    """Start the Web UI"""
    print("🌐 Starting RapidTrader Web UI...")
    
    # Change to project directory
    os.chdir(project_root)
    
    # Start Web UI
    cmd = [sys.executable, "-m", "ui_module.rapidui", "--host", "0.0.0.0", "--port", "8080"]
    return subprocess.Popen(cmd)

def create_demo_api_key():
    """Create a demo API key for testing"""
    print("🔑 Creating demo API key...")
    
    try:
        # Import auth manager
        from api_gateway.auth import auth_manager
        
        # Create demo API key
        key_id, api_key = auth_manager.generate_api_key(
            name="Demo Key",
            description="Demo API key for testing RapidTrader",
            permissions=["read", "write"]
        )
        
        print(f"✅ Demo API Key created:")
        print(f"   Key ID: {key_id}")
        print(f"   API Key: {api_key}")
        print(f"   Use this key in API requests with header: Authorization: Bearer {api_key}")
        
        return api_key
        
    except Exception as e:
        print(f"❌ Error creating demo API key: {e}")
        return None

def main():
    if len(sys.argv) < 2:
        print("Usage: python start_rapidtrader.py [api|ui|full|demo]")
        sys.exit(1)
    
    mode = sys.argv[1].lower()
    processes = []
    
    def signal_handler(sig, frame):
        print("\n🛑 Shutting down RapidTrader...")
        for process in processes:
            process.terminate()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        if mode == "api":
            processes.append(start_api_gateway())
            print("✅ API Gateway started at http://localhost:8000")
            print("📖 API Documentation: http://localhost:8000/docs")
            
        elif mode == "ui":
            processes.append(start_web_ui())
            print("✅ Web UI started at http://localhost:8080")
            
        elif mode == "full":
            processes.append(start_api_gateway())
            time.sleep(2)  # Give API Gateway time to start
            processes.append(start_web_ui())
            print("✅ RapidTrader started successfully!")
            print("🌐 Web UI: http://localhost:8080")
            print("🔌 API Gateway: http://localhost:8000")
            print("📖 API Documentation: http://localhost:8000/docs")
            
        elif mode == "demo":
            # Start API Gateway first
            processes.append(start_api_gateway())
            print("⏳ Waiting for API Gateway to start...")
            time.sleep(3)
            
            # Create demo API key
            api_key = create_demo_api_key()
            
            # Start Web UI
            processes.append(start_web_ui())
            time.sleep(2)
            
            print("\n🎉 RapidTrader Demo Mode Started!")
            print("🌐 Web UI: http://localhost:8080")
            print("🔌 API Gateway: http://localhost:8000")
            print("📖 API Documentation: http://localhost:8000/docs")
            
            if api_key:
                print(f"\n🔑 Demo API Key: {api_key}")
                print("   Use this key to test API endpoints")
                
                # Test API endpoint
                print("\n🧪 Testing API endpoint...")
                try:
                    import requests
                    response = requests.get(
                        "http://localhost:8000/health",
                        headers={"Authorization": f"Bearer {api_key}"},
                        timeout=5
                    )
                    if response.status_code == 200:
                        print("✅ API test successful!")
                    else:
                        print(f"⚠️  API test returned status: {response.status_code}")
                except Exception as e:
                    print(f"⚠️  API test failed: {e}")
            
        else:
            print("❌ Invalid mode. Use: api, ui, full, or demo")
            sys.exit(1)
        
        # Keep running
        print("\n📊 RapidTrader is running. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Shutting down RapidTrader...")
    finally:
        for process in processes:
            process.terminate()

if __name__ == "__main__":
    main()
