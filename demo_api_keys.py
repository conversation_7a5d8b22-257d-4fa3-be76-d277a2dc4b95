#!/usr/bin/env python3
"""
RapidTrader API Key Management Demo

This script demonstrates the API key management functionality of RapidTrader.
It shows how to:
1. Create new API keys
2. List existing API keys
3. Test API key authentication
4. Revoke API keys

Usage:
    python demo_api_keys.py create "My Trading Bot" "API key for automated trading"
    python demo_api_keys.py list
    python demo_api_keys.py test <api_key>
    python demo_api_keys.py revoke <key_id>
"""

import sys
import json
import requests
from pathlib import Path
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_api_key(name, description=None):
    """Create a new API key"""
    print(f"🔑 Creating API key: {name}")
    
    try:
        from api_gateway.auth import auth_manager
        
        # Create API key
        key_id, api_key = auth_manager.generate_api_key(
            name=name,
            description=description,
            permissions=["read", "write"]
        )
        
        print(f"✅ API Key created successfully!")
        print(f"   Key ID: {key_id}")
        print(f"   API Key: {api_key}")
        print(f"   Name: {name}")
        print(f"   Description: {description or 'No description'}")
        print(f"   Permissions: read, write")
        print(f"   Created: {datetime.now().isoformat()}")
        print()
        print(f"💡 Usage in API requests:")
        print(f"   curl -H 'Authorization: Bearer {api_key}' http://localhost:8000/health")
        print()
        print(f"🔒 Store this API key securely - it cannot be retrieved again!")
        
        return key_id, api_key
        
    except Exception as e:
        print(f"❌ Error creating API key: {e}")
        return None, None

def list_api_keys():
    """List all API keys"""
    print("📋 Listing all API keys:")
    
    try:
        from api_gateway.auth import auth_manager
        
        keys = auth_manager.list_api_keys()
        
        if not keys:
            print("   No API keys found.")
            return
        
        print(f"   Found {len(keys)} API key(s):")
        print()
        
        for key in keys:
            status = "🟢 Active" if key['is_active'] else "🔴 Revoked"
            print(f"   ID: {key['id']}")
            print(f"   Name: {key['name']}")
            print(f"   Description: {key['description'] or 'No description'}")
            print(f"   Status: {status}")
            print(f"   Permissions: {', '.join(key['permissions'])}")
            print(f"   Created: {key['created_at']}")
            print(f"   Last Used: {key['last_used'] or 'Never'}")
            print(f"   Usage Count: {key['usage_count']}")
            print(f"   Expires: {key['expires_at'] or 'Never'}")
            print("   " + "-" * 50)
        
    except Exception as e:
        print(f"❌ Error listing API keys: {e}")

def test_api_key(api_key):
    """Test an API key by making API requests"""
    print(f"🧪 Testing API key: {api_key[:20]}...")
    
    # Test endpoints
    endpoints = [
        ("Health Check", "GET", "http://localhost:8000/health"),
        ("Root Endpoint", "GET", "http://localhost:8000/"),
        ("List API Keys", "GET", "http://localhost:8000/auth/api-keys"),
    ]
    
    headers = {"Authorization": f"Bearer {api_key}"}
    
    for name, method, url in endpoints:
        try:
            print(f"   Testing {name}...")
            
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=5)
            else:
                response = requests.request(method, url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ {name}: Success (200)")
                if name == "Health Check":
                    data = response.json()
                    print(f"      Status: {data.get('status', 'unknown')}")
                    print(f"      Timestamp: {data.get('timestamp', 'unknown')}")
            else:
                print(f"   ⚠️  {name}: {response.status_code} - {response.text[:100]}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ {name}: Connection failed (API Gateway not running)")
        except Exception as e:
            print(f"   ❌ {name}: Error - {e}")
    
    print()
    print("💡 If tests fail, make sure the API Gateway is running:")
    print("   python start_rapidtrader.py api")

def revoke_api_key(key_id):
    """Revoke an API key"""
    print(f"🗑️  Revoking API key: {key_id}")
    
    try:
        from api_gateway.auth import auth_manager
        
        success = auth_manager.revoke_api_key(key_id)
        
        if success:
            print(f"✅ API key revoked successfully!")
        else:
            print(f"❌ API key not found or already revoked")
        
    except Exception as e:
        print(f"❌ Error revoking API key: {e}")

def main():
    if len(sys.argv) < 2:
        print("RapidTrader API Key Management Demo")
        print()
        print("Usage:")
        print("  python demo_api_keys.py create <name> [description]")
        print("  python demo_api_keys.py list")
        print("  python demo_api_keys.py test <api_key>")
        print("  python demo_api_keys.py revoke <key_id>")
        print()
        print("Examples:")
        print("  python demo_api_keys.py create 'Trading Bot' 'API key for my trading bot'")
        print("  python demo_api_keys.py list")
        print("  python demo_api_keys.py test rt_abc123...")
        print("  python demo_api_keys.py revoke key_id_123")
        return
    
    command = sys.argv[1].lower()
    
    if command == "create":
        if len(sys.argv) < 3:
            print("❌ Error: Name is required for create command")
            print("Usage: python demo_api_keys.py create <name> [description]")
            return
        
        name = sys.argv[2]
        description = sys.argv[3] if len(sys.argv) > 3 else None
        create_api_key(name, description)
        
    elif command == "list":
        list_api_keys()
        
    elif command == "test":
        if len(sys.argv) < 3:
            print("❌ Error: API key is required for test command")
            print("Usage: python demo_api_keys.py test <api_key>")
            return
        
        api_key = sys.argv[2]
        test_api_key(api_key)
        
    elif command == "revoke":
        if len(sys.argv) < 3:
            print("❌ Error: Key ID is required for revoke command")
            print("Usage: python demo_api_keys.py revoke <key_id>")
            return
        
        key_id = sys.argv[2]
        revoke_api_key(key_id)
        
    else:
        print(f"❌ Unknown command: {command}")
        print("Available commands: create, list, test, revoke")

if __name__ == "__main__":
    main()
