#!/bin/sh

# Simplified RapidTrader Build Script
# Builds optimized Docker image using the clean structure

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    printf "${GREEN}[$(date +'%H:%M:%S')] %s${NC}\n" "$1"
}

error() {
    printf "${RED}[$(date +'%H:%M:%S')] ERROR: %s${NC}\n" "$1" >&2
}

info() {
    printf "${BLUE}[$(date +'%H:%M:%S')] INFO: %s${NC}\n" "$1"
}

# Configuration
TAG=${TAG:-"latest"}
IMAGE_NAME="rapidtrader:${TAG}"

# Check Docker
if ! command -v docker >/dev/null 2>&1; then
    error "Docker not found. Please install Docker."
    exit 1
fi

log "Building RapidTrader optimized image..."

# Build using the optimized Dockerfile
docker build \
    -f docker/Dockerfile \
    -t "${IMAGE_NAME}" \
    --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
    --label "build.optimized=true" \
    . || {
        error "Failed to build image"
        exit 1
    }

# Get image size
SIZE=$(docker images "${IMAGE_NAME}" --format "{{.Size}}" | head -1)

log "Build completed successfully!"
log "Image: ${IMAGE_NAME}"
log "Size: ${SIZE}"

# Test basic functionality
log "Testing image..."
if docker run --rm "${IMAGE_NAME}" help >/dev/null 2>&1; then
    log "✅ Image test passed"
else
    error "❌ Image test failed"
    exit 1
fi

log "🚀 Ready to use: docker run --rm ${IMAGE_NAME} help"
