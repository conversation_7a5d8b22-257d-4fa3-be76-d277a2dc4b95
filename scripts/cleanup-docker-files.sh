#!/bin/sh

# RapidTrader Docker Files Cleanup Script
# Removes unnecessary and outdated Docker files, keeping only optimized versions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    printf "${GREEN}[$(date +'%H:%M:%S')] %s${NC}\n" "$1"
}

error() {
    printf "${RED}[$(date +'%H:%M:%S')] ERROR: %s${NC}\n" "$1" >&2
}

warn() {
    printf "${YELLOW}[$(date +'%H:%M:%S')] WARNING: %s${NC}\n" "$1"
}

info() {
    printf "${BLUE}[$(date +'%H:%M:%S')] INFO: %s${NC}\n" "$1"
}

# Function to calculate file size
get_size() {
    if [ -f "$1" ]; then
        du -sh "$1" 2>/dev/null | cut -f1 || echo "0"
    else
        echo "0"
    fi
}

# Function to safely remove files
safe_remove() {
    local target="$1"
    local description="$2"

    if [ -f "$target" ]; then
        local size=$(get_size "$target")
        rm -f "$target"
        info "Removed $description ($size)"
    elif [ -d "$target" ]; then
        local size=$(du -sh "$target" 2>/dev/null | cut -f1 || echo "0")
        rm -rf "$target"
        info "Removed $description ($size)"
    fi
}

# Function to clean up Docker files
cleanup_docker_files() {
    log "Cleaning up unnecessary Docker files..."

    # Remove old/unnecessary Dockerfiles
    safe_remove "Dockerfile" "old standard Dockerfile"
    safe_remove "api_gateway/Dockerfile" "API gateway Dockerfile (will use main optimized)"

    # Remove unnecessary Docker Compose files
    safe_remove "docker-compose.api.yml" "API-specific compose file"
    safe_remove "docker-compose.override.yml" "override compose file"
    safe_remove "docker-compose.production.yml" "old production compose file"
    safe_remove "docker-compose.test.yml" "test compose file"
    safe_remove "docker-compose.yml" "standard compose file"

    # Keep only the optimized compose file
    info "Keeping docker-compose.optimized.yml as the main compose file"

    # Remove old/unnecessary scripts
    safe_remove "scripts/build-optimized.sh" "old build script (replaced by build-simple.sh)"
    safe_remove "scripts/docker-control.sh" "old docker control script"
    safe_remove "scripts/docker-run.sh" "old docker run script"
    safe_remove "scripts/deploy-production.sh" "old production deploy script"
    safe_remove "scripts/rapidtrader-api.sh" "old API script"
    safe_remove "scripts/rapidtrader-docker.sh" "old docker script"
    safe_remove "scripts/test-docker-setup.sh" "old docker test script"
    safe_remove "scripts/entrypoint.sh" "old entrypoint script (replaced by entrypoint-simple.sh)"

    # Remove old requirements files
    safe_remove "api_gateway/requirements.txt" "API gateway requirements (using main optimized)"

    # Remove nginx config (not needed for optimized setup)
    safe_remove "nginx" "nginx directory (not needed for optimized setup)"

    # Remove logging configs (will use built-in logging)
    safe_remove "logging" "logging directory (using built-in logging)"
}

# Function to clean up old build artifacts
cleanup_build_artifacts() {
    log "Cleaning up Docker build artifacts..."

    # Remove old Docker images (keep only optimized)
    if command -v docker >/dev/null 2>&1; then
        info "Removing old Docker images..."

        # Remove old rapidtrader images (keep optimized)
        docker images --format "table {{.Repository}}:{{.Tag}}\t{{.ID}}" | \
            grep "rapidtrader" | \
            grep -v "optimized" | \
            awk '{print $2}' | \
            xargs -r docker rmi 2>/dev/null || true

        # Remove dangling images
        docker image prune -f >/dev/null 2>&1 || true

        info "Docker cleanup completed"
    else
        warn "Docker not available, skipping Docker image cleanup"
    fi
}

# Function to create optimized Docker structure
create_optimized_structure() {
    log "Creating optimized Docker structure..."

    # Rename optimized compose to main compose
    if [ -f "docker-compose.optimized.yml" ]; then
        cp "docker-compose.optimized.yml" "docker-compose.yml"
        info "Created main docker-compose.yml from optimized version"
    fi

    # Create simplified Docker directory structure
    mkdir -p docker/{configs,scripts}

    # Move essential Docker files to docker directory
    if [ -f "Dockerfile.alpine" ]; then
        cp "Dockerfile.alpine" "docker/Dockerfile"
        info "Created docker/Dockerfile from optimized Alpine version"
    fi

    if [ -f "scripts/entrypoint-simple.sh" ]; then
        mkdir -p "docker/scripts"
        cp "scripts/entrypoint-simple.sh" "docker/scripts/entrypoint.sh"
        chmod +x "docker/scripts/entrypoint.sh"
        info "Created docker/scripts/entrypoint.sh"
    fi

    if [ -f "requirements.optimized.txt" ]; then
        cp "requirements.optimized.txt" "docker/requirements.txt"
        info "Created docker/requirements.txt from optimized version"
    fi

    # Update Dockerfile to use new paths
    if [ -f "docker/Dockerfile" ]; then
        sed -i 's|/rapidtrader/scripts/entrypoint-simple.sh|/rapidtrader/docker/scripts/entrypoint.sh|g' "docker/Dockerfile" 2>/dev/null || true
    fi
}

# Function to update .dockerignore
update_dockerignore() {
    log "Updating .dockerignore for optimized structure..."

    # Add additional exclusions for the new clean structure
    cat >> .dockerignore << 'EOF'

# Additional exclusions after cleanup
docker/
scripts/build-*.sh
scripts/test-*.sh
scripts/cleanup-*.sh
CLEANUP_AND_OPTIMIZATION_COMPLETE.md
requirements.optimized.txt
Dockerfile.alpine

EOF

    info "Updated .dockerignore with additional exclusions"
}

# Function to create simplified build script
create_simple_build() {
    log "Creating simplified build script..."

    cat > scripts/build.sh << 'EOF'
#!/bin/sh

# Simplified RapidTrader Build Script
# Builds optimized Docker image using the clean structure

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    printf "${GREEN}[$(date +'%H:%M:%S')] %s${NC}\n" "$1"
}

error() {
    printf "${RED}[$(date +'%H:%M:%S')] ERROR: %s${NC}\n" "$1" >&2
}

# Configuration
TAG=${TAG:-"latest"}
IMAGE_NAME="rapidtrader:${TAG}"

# Check Docker
if ! command -v docker >/dev/null 2>&1; then
    error "Docker not found. Please install Docker."
    exit 1
fi

log "Building RapidTrader optimized image..."

# Build using the optimized Dockerfile
docker build \
    -f docker/Dockerfile \
    -t "${IMAGE_NAME}" \
    --label "build.date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
    --label "build.optimized=true" \
    . || {
        error "Failed to build image"
        exit 1
    }

# Get image size
SIZE=$(docker images "${IMAGE_NAME}" --format "{{.Size}}" | head -1)

log "Build completed successfully!"
log "Image: ${IMAGE_NAME}"
log "Size: ${SIZE}"

# Test basic functionality
log "Testing image..."
if docker run --rm "${IMAGE_NAME}" help >/dev/null 2>&1; then
    log "✅ Image test passed"
else
    error "❌ Image test failed"
    exit 1
fi

log "🚀 Ready to use: docker run --rm ${IMAGE_NAME} help"
EOF

    chmod +x scripts/build.sh
    info "Created simplified scripts/build.sh"
}

# Function to show cleanup summary
show_summary() {
    log "Docker Cleanup Summary:"
    echo ""

    info "Files Removed:"
    echo "  🗑️  Old Dockerfiles (Dockerfile, api_gateway/Dockerfile)"
    echo "  🗑️  Multiple compose files (keeping optimized as main)"
    echo "  🗑️  Old build and deployment scripts"
    echo "  🗑️  Nginx and logging configurations"
    echo "  🗑️  Old Docker images (keeping optimized)"
    echo ""

    info "Optimized Structure Created:"
    echo "  ✅ docker/Dockerfile (optimized Alpine)"
    echo "  ✅ docker/scripts/entrypoint.sh (simplified)"
    echo "  ✅ docker/requirements.txt (optimized)"
    echo "  ✅ docker-compose.yml (from optimized)"
    echo "  ✅ scripts/build.sh (simplified)"
    echo ""

    info "Current Docker Files:"
    if [ -f "docker-compose.yml" ]; then
        echo "  📄 docker-compose.yml ($(get_size docker-compose.yml))"
    fi
    if [ -f "docker/Dockerfile" ]; then
        echo "  📄 docker/Dockerfile ($(get_size docker/Dockerfile))"
    fi
    if [ -f ".dockerignore" ]; then
        echo "  📄 .dockerignore ($(get_size .dockerignore))"
    fi
    echo ""

    info "Usage:"
    echo "  Build: ./scripts/build.sh"
    echo "  Run:   docker-compose up"
    echo "  Test:  docker run --rm rapidtrader help"
}

# Main execution
main() {
    log "🧹 RapidTrader Docker Files Cleanup"

    # Parse command line arguments
    while [ $# -gt 0 ]; do
        case $1 in
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --keep-old)
                KEEP_OLD=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --dry-run     Show what would be removed without actually removing"
                echo "  --keep-old    Keep old Docker images"
                echo "  --help        Show this help"
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                exit 1
                ;;
        esac
    done

    if [ "$DRY_RUN" = "true" ]; then
        warn "DRY RUN MODE - No files will be actually removed"
    fi

    # Perform cleanup
    cleanup_docker_files

    if [ "$KEEP_OLD" != "true" ]; then
        cleanup_build_artifacts
    fi

    create_optimized_structure
    update_dockerignore
    create_simple_build

    # Show summary
    show_summary

    log "✅ Docker cleanup completed!"
    echo ""
    info "Next steps:"
    echo "  1. Build optimized image: ./scripts/build.sh"
    echo "  2. Test the setup: docker run --rm rapidtrader help"
    echo "  3. Deploy: docker-compose up"
}

# Execute main function
main "$@"
