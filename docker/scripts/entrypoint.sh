#!/bin/sh

# RapidTrader Simple Docker Entrypoint Script
# Compatible with Alpine Linux sh

set -e

# Performance optimizations
export MALLOC_TRIM_THRESHOLD_=100000
export MALLOC_MMAP_THRESHOLD_=131072
export PYTHONOPTIMIZE=2
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1

# Simple logging function
log() {
    echo "[$(date +'%H:%M:%S')] $1"
}

# Create required directories
check_directories() {
    for dir in userdata userdata/config userdata/logs userdata/data cache; do
        if [ ! -d "/rapidtrader/$dir" ]; then
            mkdir -p "/rapidtrader/$dir"
        fi
    done
}

# Set default config file if not provided
if [ -z "$CONFIG_FILE" ]; then
    CONFIG_FILE="/rapidtrader/userdata/config/config.json"
fi

# Get the command
MODE=${1:-help}

# Fast initialization
check_directories

case $MODE in
    "help")
        echo "RapidTrader Optimized Container"
        echo ""
        echo "Commands:"
        echo "  backtest [--optimized]     - High-performance backtesting"
        echo "  dryrun [--optimized]       - Optimized dry trading"
        echo "  live [--optimized]         - Fast live trading"
        echo "  optimize                   - Strategy optimization"
        echo "  shell                      - Interactive shell"
        echo "  data                       - Data management"
        echo "  trade                      - Trading commands"
        echo "  paper-trade               - Independent paper trading"
        echo ""
        echo "Optimizations:"
        echo "  --optimized               - Enable all performance optimizations"
        echo "  --batch-orders            - Enable order batching"
        echo "  --async-data              - Enable async data processing"
        echo "  --fast-execution          - Enable fast execution mode"
        echo ""
        echo "Examples:"
        echo "  docker run rapidtrader:optimized backtest --optimized"
        echo "  docker run rapidtrader:optimized dryrun --optimized --batch-orders"
        echo "  docker run rapidtrader:optimized live --optimized --fast-execution"
        ;;

    "backtest")
        log "🔄 Optimized backtesting"
        shift
        if echo "$*" | grep -q -- "--optimized"; then
            export RAPIDTRADER_OPTIMIZATION=true
            export RAPIDTRADER_CACHE_SIZE=2000
            exec python -m core.optimized_backtest_engine "$@"
        else
            exec python /rapidtrader/rapidtrader backtest run -c "$CONFIG_FILE" "$@"
        fi
        ;;

    "dryrun")
        log "📊 Optimized dry run"
        shift
        if echo "$*" | grep -q -- "--optimized"; then
            export RAPIDTRADER_BATCH_ORDERS=true
            export RAPIDTRADER_ASYNC_DATA=true
            exec python -m core.dryrun "$@"
        else
            exec python /rapidtrader/rapidtrader trade dryrun -c "$CONFIG_FILE" "$@"
        fi
        ;;

    "paper-trade"|"paper")
        log "📝 Independent paper trading"
        shift
        exec python /rapidtrader/rapidtrader paper-trade start -c "$CONFIG_FILE" "$@"
        ;;

    "live")
        log "💰 Optimized live trading"
        echo "⚠️  LIVE TRADING - REAL MONEY"
        shift
        if echo "$*" | grep -q -- "--optimized"; then
            export RAPIDTRADER_FAST_EXECUTION=true
            export RAPIDTRADER_CONNECTION_POOL=true
            exec python /rapidtrader/rapidtrader trade start --optimized -c "$CONFIG_FILE" "$@"
        else
            exec python /rapidtrader/rapidtrader trade start -c "$CONFIG_FILE" "$@"
        fi
        ;;

    "optimize")
        log "⚡ Strategy optimization"
        shift
        exec python /rapidtrader/rapidtrader backtest optimize -c "$CONFIG_FILE" "$@"
        ;;

    "data")
        log "📈 Data management"
        shift
        exec python -m data.download_data "$@"
        ;;

    "shell")
        log "🐚 Interactive shell"
        exec /bin/sh
        ;;

    *)
        exec python /rapidtrader/rapidtrader "$@"
        ;;
esac
