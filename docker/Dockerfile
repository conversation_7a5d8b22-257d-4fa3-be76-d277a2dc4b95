# Multi-stage Alpine-based Dockerfile for RapidTrader
# Ultra-optimized for production with minimal size and maximum performance

# Build stage
FROM python:3.11-alpine AS builder

LABEL maintainer="RapidTrader <<EMAIL>>"

# Install only essential build dependencies
RUN apk add --no-cache --virtual .build-deps \
    build-base \
    libffi-dev \
    openssl-dev \
    musl-dev \
    linux-headers \
    git \
    && rm -rf /var/cache/apk/*

# Create optimized virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and optimize requirements
COPY requirements.txt /tmp/
RUN pip install --upgrade pip wheel setuptools \
    && pip install --no-cache-dir --no-deps --compile -r /tmp/requirements.txt \
    && find /opt/venv -name "*.pyc" -delete \
    && find /opt/venv -name "__pycache__" -type d -exec rm -rf {} + \
    && find /opt/venv -name "*.pyo" -delete

# Production stage - Ultra-minimal runtime
FROM python:3.11-alpine AS production

# Optimized environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONOPTIMIZE=2 \
    PATH="/opt/venv/bin:$PATH" \
    RAPIDTRADER_ENV=production \
    MALLOC_TRIM_THRESHOLD_=100000 \
    MALLOC_MMAP_THRESHOLD_=131072

# Install only essential runtime dependencies
RUN apk add --no-cache \
    libffi \
    openssl \
    ca-certificates \
    tzdata \
    && rm -rf /var/cache/apk/* /tmp/* /var/tmp/*

# Create optimized user
RUN addgroup -g 1000 rapidtrader \
    && adduser -D -u 1000 -G rapidtrader -s /bin/sh rapidtrader

# Copy optimized virtual environment
COPY --from=builder /opt/venv /opt/venv

# Create minimal directory structure
RUN mkdir -p /rapidtrader/{userdata,logs,cache} \
    && chown -R rapidtrader:rapidtrader /rapidtrader

WORKDIR /rapidtrader

# Copy application files (filtered by .dockerignore)
COPY --chown=rapidtrader:rapidtrader . /rapidtrader/

# Optimize file permissions and cleanup
RUN chmod +x /rapidtrader/rapidtrader /rapidtrader/scripts/*.sh 2>/dev/null || true \
    && find /rapidtrader -name "*.pyc" -delete \
    && find /rapidtrader -name "__pycache__" -type d -exec rm -rf {} + \
    && find /rapidtrader -name "*.pyo" -delete

# Switch to non-root user
USER rapidtrader

# Create optimized symbolic links
RUN mkdir -p /home/<USER>/.local/bin \
    && ln -sf /rapidtrader/rapidtrader /home/<USER>/.local/bin/rapidtrader

# Optimized volumes for performance
VOLUME ["/rapidtrader/userdata", "/rapidtrader/logs", "/rapidtrader/cache"]

# Enhanced health check
HEALTHCHECK --interval=60s --timeout=5s --start-period=10s --retries=2 \
    CMD python -c "import core.rapidtrader; print('OK')" || exit 1

# Optimized entrypoint
ENTRYPOINT ["/rapidtrader/docker/scripts/entrypoint.sh"]
CMD ["help"]
