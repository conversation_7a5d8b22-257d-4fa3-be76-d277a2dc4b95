#!/usr/bin/env python3
"""
Quick Fyers Token Generator
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv, set_key

# Load environment
load_dotenv()

# Import Fyers
try:
    from fyers_apiv3 import fyersModel
    print("✅ Fyers API imported successfully")
except ImportError as e:
    print(f"❌ Failed to import Fyers API: {e}")
    sys.exit(1)

def main():
    print("🔄 Quick Fyers Token Generation")
    print("=" * 40)
    
    # Get credentials
    client_id = os.getenv('FYERS_CLIENT_ID')
    secret_key = os.getenv('FYERS_SECRET_KEY')
    redirect_uri = os.getenv('FYERS_REDIRECT_URI', 'https://127.0.0.1/')
    auth_code = os.getenv('FYERS_AUTH_CODE')
    
    print(f"Client ID: {client_id}")
    print(f"Secret Key: {secret_key[:8]}..." if secret_key else "Not set")
    print(f"Auth Code: {auth_code[:50]}..." if auth_code else "Not set")
    
    if not all([client_id, secret_key, auth_code]):
        print("❌ Missing required credentials")
        return False
    
    try:
        print("\n🔄 Creating session...")
        session = fyersModel.SessionModel(
            client_id=client_id,
            secret_key=secret_key,
            redirect_uri=redirect_uri,
            response_type="code",
            grant_type="authorization_code"
        )
        
        print("🔄 Setting auth code...")
        session.set_token(auth_code)
        
        print("🔄 Generating access token...")
        response = session.generate_token()
        
        print(f"\n📋 Response: {response}")
        
        if response.get('s') == 'ok':
            access_token = response.get('access_token')
            refresh_token = response.get('refresh_token', '')
            
            print(f"\n✅ Success!")
            print(f"Access Token: {access_token[:50]}...")
            if refresh_token:
                print(f"Refresh Token: {refresh_token[:50]}...")
            
            # Update .env file
            print("\n💾 Updating .env file...")
            env_file = Path('.env')
            set_key(env_file, 'FYERS_ACCESS_TOKEN', access_token)
            if refresh_token:
                set_key(env_file, 'FYERS_REFRESH_TOKEN', refresh_token)
            
            print("✅ .env file updated!")
            
            # Test connection
            print("\n🧪 Testing connection...")
            fyers = fyersModel.FyersModel(
                client_id=client_id,
                token=access_token,
                log_path=""
            )
            
            profile = fyers.get_profile()
            print(f"Profile response: {profile}")
            
            if profile.get('s') == 'ok':
                user_data = profile.get('data', {})
                print("✅ Connection successful!")
                print(f"   User ID: {user_data.get('fy_id', 'N/A')}")
                print(f"   Name: {user_data.get('name', 'N/A')}")
                return True
            else:
                print("❌ Connection test failed")
                return False
                
        else:
            print(f"❌ Token generation failed: {response}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Fyers authentication completed!")
        print("Now restart RapidTrader to see Fyers connected:")
        print("   python start_rapidtrader.py demo")
    else:
        print("\n❌ Authentication failed")
