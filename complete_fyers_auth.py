#!/usr/bin/env python3
"""
Complete Fyers Authentication

This script takes the auth code from the redirect URL and generates access tokens.
Usage: python complete_fyers_auth.py <auth_code>
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv, set_key

# Load environment
load_dotenv()

def generate_token(auth_code):
    """Generate access token from auth code."""
    
    print("🔄 Fyers Token Generation")
    print("=" * 40)
    
    # Get credentials
    client_id = os.getenv('FYERS_CLIENT_ID')
    secret_key = os.getenv('FYERS_SECRET_KEY')
    redirect_uri = os.getenv('FYERS_REDIRECT_URI', 'https://127.0.0.1/')
    
    print(f"Client ID: {client_id}")
    print(f"Secret Key: {secret_key[:8]}..." if secret_key else "Not set")
    print(f"Auth Code: {auth_code[:50]}...")
    
    if not all([client_id, secret_key, auth_code]):
        print("❌ Missing required credentials")
        return False
    
    try:
        # Import Fyers API
        from fyers_apiv3 import fyersModel
        print("✅ Fyers API imported successfully")
        
        # Create session
        print("\n🔄 Creating session...")
        session = fyersModel.SessionModel(
            client_id=client_id,
            secret_key=secret_key,
            redirect_uri=redirect_uri,
            response_type="code",
            grant_type="authorization_code"
        )
        
        # Set auth code
        print("🔄 Setting auth code...")
        session.set_token(auth_code)
        
        # Generate token
        print("🔄 Generating access token...")
        response = session.generate_token()
        
        print(f"\n📋 Response: {response}")
        
        if response.get('s') == 'ok':
            access_token = response.get('access_token')
            refresh_token = response.get('refresh_token', '')
            
            print(f"\n✅ Success!")
            print(f"Access Token: {access_token[:50]}...")
            if refresh_token:
                print(f"Refresh Token: {refresh_token[:50]}...")
            
            # Update .env file
            print("\n💾 Updating .env file...")
            env_file = Path('.env')
            set_key(env_file, 'FYERS_AUTH_CODE', auth_code)
            set_key(env_file, 'FYERS_ACCESS_TOKEN', access_token)
            if refresh_token:
                set_key(env_file, 'FYERS_REFRESH_TOKEN', refresh_token)
            
            print("✅ .env file updated!")
            
            # Test connection
            print("\n🧪 Testing connection...")
            fyers = fyersModel.FyersModel(
                client_id=client_id,
                token=access_token,
                log_path=""
            )
            
            profile = fyers.get_profile()
            print(f"Profile response: {profile}")
            
            if profile.get('s') == 'ok':
                user_data = profile.get('data', {})
                print("✅ Connection successful!")
                print(f"   User ID: {user_data.get('fy_id', 'N/A')}")
                print(f"   Name: {user_data.get('name', 'N/A')}")
                print(f"   Email: {user_data.get('email_id', 'N/A')}")
                
                # Test funds API
                try:
                    funds = fyers.funds()
                    if funds.get('s') == 'ok':
                        print("✅ Funds API working!")
                        fund_data = funds.get('fund_limit', [])
                        if fund_data and isinstance(fund_data, list) and fund_data:
                            available_cash = fund_data[0].get('availablecash', 0)
                            print(f"   Available Balance: ₹{available_cash:,.2f}")
                except Exception as e:
                    print(f"⚠️  Funds API test failed: {e}")
                
                return True
            else:
                print("❌ Connection test failed")
                return False
                
        else:
            print(f"❌ Token generation failed: {response}")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import Fyers API: {e}")
        print("Try: pip install fyers-apiv3")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main function."""
    print("🎯 Fyers Authentication - Step 2: Generate Token")
    print("=" * 60)
    
    if len(sys.argv) < 2:
        print("Usage: python complete_fyers_auth.py <auth_code>")
        print("\nExample:")
        print("python complete_fyers_auth.py eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        print("\nOr paste the entire redirect URL:")
        print("python complete_fyers_auth.py 'https://127.0.0.1/?s=ok&code=200&auth_code=YOUR_CODE&state=sample_state'")
        return
    
    auth_input = sys.argv[1]
    
    # Extract auth code from URL if full URL is provided
    if auth_input.startswith('https://') and 'auth_code=' in auth_input:
        try:
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(auth_input)
            query_params = parse_qs(parsed_url.query)
            auth_code = query_params.get('auth_code', [None])[0]
            if not auth_code:
                print("❌ Could not extract auth_code from URL")
                return
            print(f"📎 Extracted auth code from URL: {auth_code[:50]}...")
        except Exception as e:
            print(f"❌ Error parsing URL: {e}")
            return
    else:
        auth_code = auth_input
    
    success = generate_token(auth_code)
    
    if success:
        print("\n🎉 Fyers authentication completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart RapidTrader to use the new tokens:")
        print("   python start_rapidtrader.py demo")
        print("2. Check the frontend - Fyers should now show as connected")
        print("3. Both DhanHQ and Fyers will be operational")
        print("4. Test trading in dry-run mode")
    else:
        print("\n❌ Authentication failed")
        print("Please check the error messages above and try again")

if __name__ == "__main__":
    main()
