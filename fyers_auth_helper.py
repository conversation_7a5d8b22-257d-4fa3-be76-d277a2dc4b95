#!/usr/bin/env python3
"""
Fyers Authentication Helper for RapidTrader

This script helps you authenticate with Fyers API and get fresh access tokens.
It handles the complete OAuth2 flow including TOTP generation.

Usage:
    python fyers_auth_helper.py
"""

import os
import sys
import time
import pyotp
import webbrowser
from pathlib import Path
from dotenv import load_dotenv, set_key

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Load environment variables
load_dotenv()

try:
    from fyers_apiv3 import fyersModel
    _has_fyers_api = True
except ImportError:
    _has_fyers_api = False
    print("❌ fyers-apiv3 package not found. Installing...")
    os.system("pip install fyers-apiv3")
    try:
        from fyers_apiv3 import fyersModel
        _has_fyers_api = True
        print("✅ fyers-apiv3 installed successfully")
    except ImportError:
        print("❌ Failed to install fyers-apiv3")
        sys.exit(1)

def get_fyers_credentials():
    """Get Fyers credentials from environment or user input."""
    print("🔐 Fyers API Authentication Setup")
    print("=" * 50)

    # Get credentials from .env or prompt user
    client_id = os.getenv('FYERS_CLIENT_ID')
    secret_key = os.getenv('FYERS_SECRET_KEY')
    redirect_uri = os.getenv('FYERS_REDIRECT_URI', 'https://127.0.0.1/')
    totp_key = os.getenv('FYERS_TOTP_KEY')
    pin = os.getenv('FYERS_PIN')
    fy_id = os.getenv('FYERS_FY_ID')

    print(f"Client ID: {client_id}")
    print(f"Secret Key: {secret_key[:8]}..." if secret_key else "Not set")
    print(f"Redirect URI: {redirect_uri}")
    print(f"FY ID: {fy_id}")
    print(f"TOTP Key: {'Set' if totp_key else 'Not set'}")
    print(f"PIN: {'Set' if pin else 'Not set'}")

    # Check if we have minimum required credentials
    if not client_id or not secret_key:
        print("\n❌ Missing required Fyers credentials in .env file")
        print("Please ensure these are set in your .env file:")
        print("- FYERS_CLIENT_ID")
        print("- FYERS_SECRET_KEY")
        return None

    return {
        'client_id': client_id,
        'secret_key': secret_key,
        'redirect_uri': redirect_uri,
        'totp_key': totp_key,
        'pin': pin,
        'fy_id': fy_id
    }

def generate_totp(totp_key):
    """Generate TOTP code."""
    try:
        totp = pyotp.TOTP(totp_key)
        return totp.now()
    except Exception as e:
        print(f"❌ Error generating TOTP: {e}")
        return None

def authenticate_fyers(credentials):
    """Perform Fyers authentication flow."""
    print("\n🚀 Starting Fyers Authentication...")

    try:
        # Step 1: Create session
        session = fyersModel.SessionModel(
            client_id=credentials['client_id'],
            secret_key=credentials['secret_key'],
            redirect_uri=credentials['redirect_uri'],
            response_type="code",
            grant_type="authorization_code"
        )

        # Step 2: Generate auth URL
        auth_url = session.generate_authcode()
        print(f"\n📱 Opening authentication URL in browser...")
        print(f"URL: {auth_url}")

        # Open browser
        webbrowser.open(auth_url)

        # Step 3: Get auth code from user
        print("\n⏳ Please complete the authentication in your browser...")
        print("After logging in, you'll be redirected to a URL like:")
        print("https://127.0.0.1/?code=YOUR_AUTH_CODE&state=...")

        auth_code = input("\n🔑 Enter the 'code' parameter from the redirect URL: ").strip()

        if not auth_code:
            print("❌ No auth code provided")
            return None

        # Step 4: Set auth code in session
        session.set_token(auth_code)

        # Step 5: Generate access token
        print("\n🔄 Generating access token...")
        response = session.generate_token()

        if response.get('s') == 'ok':
            access_token = response['access_token']
            refresh_token = response.get('refresh_token', '')

            print("✅ Authentication successful!")
            print(f"Access Token: {access_token[:50]}...")
            print(f"Refresh Token: {refresh_token[:50]}..." if refresh_token else "No refresh token")

            # Update .env file
            env_file = project_root / '.env'
            set_key(env_file, 'FYERS_AUTH_CODE', auth_code)
            set_key(env_file, 'FYERS_ACCESS_TOKEN', access_token)
            if refresh_token:
                set_key(env_file, 'FYERS_REFRESH_TOKEN', refresh_token)

            print(f"\n💾 Tokens saved to .env file")

            return {
                'auth_code': auth_code,
                'access_token': access_token,
                'refresh_token': refresh_token
            }
        else:
            print(f"❌ Authentication failed: {response}")
            return None

    except Exception as e:
        print(f"❌ Error during authentication: {e}")
        return None

def test_connection(credentials, tokens):
    """Test the Fyers connection."""
    print("\n🧪 Testing Fyers connection...")

    try:
        # Create Fyers client
        fyers = fyersModel.FyersModel(
            client_id=credentials['client_id'],
            token=tokens['access_token'],
            log_path=""
        )

        # Test profile API
        profile = fyers.get_profile()

        if profile.get('s') == 'ok':
            user_data = profile.get('data', {})
            print("✅ Connection successful!")
            print(f"User ID: {user_data.get('fy_id', 'N/A')}")
            print(f"Name: {user_data.get('name', 'N/A')}")
            print(f"Email: {user_data.get('email_id', 'N/A')}")
            print(f"Mobile: {user_data.get('mobile_number', 'N/A')}")
            return True
        else:
            print(f"❌ Connection test failed: {profile}")
            return False

    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def main():
    """Main function."""
    print("🎯 Fyers Authentication Helper for RapidTrader")
    print("=" * 60)

    # Check if pyotp is installed
    try:
        import pyotp
    except ImportError:
        print("📦 Installing pyotp for TOTP generation...")
        os.system("pip install pyotp")
        import pyotp

    # Get credentials
    credentials = get_fyers_credentials()
    if not credentials:
        return

    # Generate TOTP
    totp_code = generate_totp(credentials['totp_key'])
    if totp_code:
        print(f"\n🔢 Current TOTP Code: {totp_code}")
        print("(Use this if prompted during login)")

    # Authenticate
    tokens = authenticate_fyers(credentials)
    if not tokens:
        print("\n❌ Authentication failed")
        return

    # Test connection
    if test_connection(credentials, tokens):
        print("\n🎉 Fyers authentication completed successfully!")
        print("\n📋 Next steps:")
        print("1. Restart RapidTrader to use the new tokens")
        print("2. Check the frontend - Fyers should now show as connected")
        print("3. Test trading in dry-run mode")

        print(f"\n🔧 To restart RapidTrader:")
        print(f"   python start_rapidtrader.py demo")
    else:
        print("\n❌ Authentication completed but connection test failed")
        print("Please check your credentials and try again")

if __name__ == "__main__":
    main()
