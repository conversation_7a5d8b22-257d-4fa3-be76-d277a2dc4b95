#!/usr/bin/env python3
"""
Test Fyers Import and Connection

This script tests if Fyers API is properly installed and can connect.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment
load_dotenv()

def test_fyers_import():
    """Test Fyers API import."""
    print("🧪 Testing Fyers API Import")
    print("=" * 40)
    
    # Test basic import
    try:
        import fyers_apiv3
        print("✅ fyers_apiv3 package imported successfully")
        print(f"   Location: {fyers_apiv3.__file__}")
    except ImportError as e:
        print(f"❌ fyers_apiv3 import failed: {e}")
        return False
    
    # Test fyersModel import
    try:
        from fyers_apiv3 import fyersModel
        print("✅ fyersModel imported successfully")
    except ImportError as e:
        print(f"❌ fyersModel import failed: {e}")
        return False
    
    return True

def test_fyers_broker():
    """Test FyersBroker initialization."""
    print("\n🧪 Testing FyersBroker Initialization")
    print("=" * 40)
    
    try:
        from broker.fyers_wrapper import FyersBroker
        print("✅ FyersBroker imported successfully")
        
        # Try to create broker instance
        broker = FyersBroker()
        print("✅ FyersBroker instance created")
        
        # Check connection status
        if broker.is_connected():
            print("✅ Fyers broker is connected")
            
            # Test profile API
            profile = broker.get_profile()
            if profile.get('s') == 'ok':
                user_data = profile.get('data', {})
                print("✅ Profile API working")
                print(f"   User: {user_data.get('name', 'N/A')}")
                print(f"   Email: {user_data.get('email_id', 'N/A')}")
                return True
            else:
                print(f"❌ Profile API failed: {profile}")
                return False
        else:
            print("❌ Fyers broker not connected")
            print(f"   Client ID: {broker.client_id}")
            print(f"   Access Token: {'Set' if broker.access_token else 'Not set'}")
            return False
            
    except Exception as e:
        print(f"❌ FyersBroker test failed: {e}")
        return False

def test_environment():
    """Test environment variables."""
    print("\n🧪 Testing Environment Variables")
    print("=" * 40)
    
    required_vars = [
        'FYERS_CLIENT_ID',
        'FYERS_ACCESS_TOKEN',
        'FYERS_SECRET_KEY'
    ]
    
    all_set = True
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:20]}...")
        else:
            print(f"❌ {var}: Not set")
            all_set = False
    
    return all_set

def main():
    """Main test function."""
    print("🎯 RapidTrader Fyers Integration Test")
    print("=" * 50)
    
    # Test 1: Environment variables
    env_ok = test_environment()
    
    # Test 2: Fyers API import
    import_ok = test_fyers_import()
    
    # Test 3: FyersBroker
    broker_ok = test_fyers_broker()
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 30)
    print(f"Environment Variables: {'✅ Pass' if env_ok else '❌ Fail'}")
    print(f"Fyers API Import:      {'✅ Pass' if import_ok else '❌ Fail'}")
    print(f"FyersBroker Test:      {'✅ Pass' if broker_ok else '❌ Fail'}")
    
    if all([env_ok, import_ok, broker_ok]):
        print("\n🎉 All tests passed! Fyers integration is working.")
        return True
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
